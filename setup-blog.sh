#!/bin/bash

echo "🚀 Setting up Blog Management System for Visceral Fat Calculator"
echo ""

# Navigate to website directory
cd website

# Install Node.js dependencies
echo "📦 Installing dependencies..."
npm install marked gray-matter

# Make CLI executable
chmod +x admin/blog-cli.js

# Generate initial blog posts
echo ""
echo "🔄 Generating initial blog posts..."
node admin/blog-cli.js generate

echo ""
echo "✅ Blog system setup complete!"
echo ""
echo "📝 Usage:"
echo "  • Edit markdown files in: website/blog-content/"
echo "  • Run 'npm run blog:generate' to create HTML"
echo "  • Run 'npm run blog:list' to see all posts"
echo "  • Open website/admin/index.html for web interface"
echo ""
echo "🌐 Admin Interface: file://$(pwd)/admin/index.html"
echo "📁 Content Folder: $(pwd)/blog-content/"
echo "📄 Generated HTML: $(pwd)/blog/"
echo ""
