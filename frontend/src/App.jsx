import { useState } from "react";
import "./App.css";

function App() {
  const [formData, setFormData] = useState({
    sex: "male",
    age: "",
    weight: "",
    height: "",
    waist: "",
    thigh: "",
  });

  const [result, setResult] = useState(null);

  const handleChange = (e) => {
    const value = e.target.value;
    setFormData({
      ...formData,
      [e.target.id]: e.target.id === "sex" ? value : Number(value),
    });
  };

  const handleSubmit = async () => {
    try {
      const response = await fetch("http://localhost:3001/api/calculate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errText = await response.text();
        throw new Error(`Server error: ${errText}`);
      }

      const data = await response.json();
      setResult(data);
    } catch (error) {
      console.error("Fetch error:", error);
      alert("Error calculating VAT. Check console for details.");
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-6">
      <div className="bg-white shadow-lg rounded-lg p-8 w-full max-w-lg">
        <h1 className="text-2xl font-bold mb-6 text-center text-indigo-700">
          Visceral Fat Calculator
        </h1>

        {/* Form */}
        <div className="space-y-4">
          <div>
            <label className="block text-gray-700 font-medium mb-1">Sex</label>
            <select
              id="sex"
              value={formData.sex}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2"
            >
              <option value="male">Male</option>
              <option value="female">Female</option>
            </select>
          </div>

          {["age", "weight", "height", "waist", "thigh"].map((field) => (
            <div key={field}>
              <label className="block text-gray-700 font-medium mb-1 capitalize">
                {field}{" "}
                {field === "age"
                  ? "(years)"
                  : field === "weight"
                  ? "(kg)"
                  : "(cm)"}
              </label>
              <input
                type="number"
                id={field}
                value={formData[field]}
                onChange={handleChange}
                className="w-full border rounded px-3 py-2"
              />
            </div>
          ))}

          <button
            type="button"
            onClick={handleSubmit}
            className="w-full bg-indigo-600 text-white py-2 rounded hover:bg-indigo-700 transition"
          >
            Calculate
          </button>
        </div>

        {/* Results */}
        {result && (
          <div className="mt-6 bg-gray-50 border rounded p-4">
            <h2 className="text-xl font-semibold text-gray-800 mb-2">Results</h2>
            <p>
              <strong>BMI:</strong> {result.bmi} kg/m²
            </p>
            <p>
              <strong>VAT:</strong> {result.vat} cm²
            </p>
            <p>
              <strong>Interpretation:</strong>{" "}
              <span
                className={
                  result.interpretation.color === "green"
                    ? "text-green-600 font-bold"
                    : result.interpretation.color === "orange"
                    ? "text-orange-600 font-bold"
                    : "text-red-600 font-bold"
                }
              >
                {result.interpretation.level}
              </span>
            </p>

            <h3 className="font-semibold mt-3">Recommendations:</h3>
            <ul className="list-disc ml-5 text-sm">
              {result.interpretation.recommendations.map((rec, idx) => (
                <li key={idx} className="mb-2">
                  {rec}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}

export default App;
