import { useState } from "react";
import "./App.css";

function App() {
  const [formData, setFormData] = useState({
    sex: "male",
    age: "",
    weight: "",
    height: "",
    waist: "",
    thigh: "",
  });

  const [result, setResult] = useState(null);

  const handleChange = (e) => {
    const value = e.target.value;
    setFormData({
      ...formData,
      [e.target.id]: e.target.id === "sex" ? value : Number(value),
    });
  };

  const handleSubmit = async () => {
    try {
      const response = await fetch("http://localhost:3001/api/calculate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errText = await response.text();
        throw new Error(`Server error: ${errText}`);
      }

      const data = await response.json();
      setResult(data);
    } catch (error) {
      console.error("Fetch error:", error);
      alert("Error calculating VAT. Check console for details.");
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-6">
      <div className="bg-white shadow-lg rounded-lg p-8 w-full max-w-lg">
        <h1 className="text-2xl font-bold mb-4 text-center text-indigo-700">
          Visceral Fat Calculator
        </h1>

        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-3 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                <strong>Educational Use Only:</strong> This tool is for educational purposes only and is NOT a medical device.
                Always consult healthcare professionals for medical advice.
              </p>
            </div>
          </div>
        </div>

        {/* Form */}
        <div className="space-y-4">
          <div>
            <label className="block text-gray-700 font-medium mb-1">Sex</label>
            <select
              id="sex"
              value={formData.sex}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2"
            >
              <option value="male">Male</option>
              <option value="female">Female</option>
            </select>
          </div>

          {["age", "weight", "height", "waist", "thigh"].map((field) => (
            <div key={field}>
              <label className="block text-gray-700 font-medium mb-1 capitalize">
                {field}{" "}
                {field === "age"
                  ? "(years)"
                  : field === "weight"
                  ? "(kg)"
                  : "(cm)"}
              </label>
              <input
                type="number"
                id={field}
                value={formData[field]}
                onChange={handleChange}
                className="w-full border rounded px-3 py-2"
              />
            </div>
          ))}

          <button
            type="button"
            onClick={handleSubmit}
            className="w-full bg-indigo-600 text-white py-2 rounded hover:bg-indigo-700 transition"
          >
            Calculate
          </button>
        </div>

        {/* Results */}
        {result && (
          <div className="mt-6 bg-gray-50 border rounded p-4">
            <h2 className="text-xl font-semibold text-gray-800 mb-2">Results</h2>
            <p>
              <strong>BMI:</strong> {result.bmi} kg/m²
            </p>
            <p>
              <strong>VAT:</strong> {result.vat} cm²
            </p>
            <p>
              <strong>Interpretation:</strong>{" "}
              <span
                className={
                  result.interpretation.color === "green"
                    ? "text-green-600 font-bold"
                    : result.interpretation.color === "orange"
                    ? "text-orange-600 font-bold"
                    : "text-red-600 font-bold"
                }
              >
                {result.interpretation.level}
              </span>
            </p>

            <h3 className="font-semibold mt-3">Recommendations:</h3>
            <ul className="list-disc ml-5 text-sm">
              {result.interpretation.recommendations.map((rec, idx) => (
                <li key={idx} className="mb-2">
                  {rec}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Footer with references */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <div className="text-xs text-gray-500 space-y-2">
            <p className="font-semibold">Scientific References:</p>
            <ul className="space-y-1 text-xs">
              <li>• Ryo, M., et al. (2005). Diabetes Care, 28(2), 451-453.</li>
              <li>• Amato, M. C., et al. (2010). Diabetes Care, 33(4), 920-922.</li>
              <li>• Yoshizumi, T., et al. (1999). Radiology, 211(1), 283-286.</li>
            </ul>
            <p className="pt-2 text-center">
              © 2025 Visceral Fat Calculator. Proprietary Software. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
