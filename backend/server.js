const express = require("express");
const bodyParser = require("body-parser");
const cors = require("cors");

const app = express();
app.use(cors());
app.use(bodyParser.json());

/**
 * VISCERAL FAT CALCULATOR - PROPRIETARY SOFTWARE
 * Copyright (c) 2025. All rights reserved.
 *
 * EDUCATIONAL USE ONLY - NOT A MEDICAL DEVICE
 * This software is for educational and research purposes only.
 * It is NOT intended for medical diagnosis, treatment, or clinical decision-making.
 * Always consult qualified healthcare professionals for medical advice.
 *
 * VAT calculation formulas based on validated research:
 *
 * SCIENTIFIC REFERENCES:
 * 1. <PERSON><PERSON>, <PERSON>, et al. (2005). "A new simple method for the measurement of visceral fat accumulation
 *    by bioelectrical impedance analysis." Diabetes Care, 28(2), 451-453.
 * 2. <PERSON>, M. C., et al. (2010). "Visceral Adiposity Index: a reliable indicator of visceral fat
 *    function associated with cardiometabolic risk." Diabetes Care, 33(4), 920-922.
 * 3. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, et al. (1999). "Abdominal fat: standardized technique for measurement at CT."
 *    Radiology, 211(1), 283-286.
 *
 * FORMULAS:
 * - Men: VAT = 6*WC – 4.41*TC + 1.19*Age – 213.65
 * - Women: VAT = 2.15*WC – 3.63*TC + 1.46*Age + 6.22*BMI – 92.713
 *
 * Where: WC = Waist Circumference (cm), TC = Thigh Circumference (cm), BMI = Body Mass Index (kg/m²)
 */
function calculateVAT(sex, age, weight, height, waist, thigh) {
  const bmi = weight / ((height / 100) ** 2);
  let vat;

  if (sex === "male") {
    vat = 6 * waist - 4.41 * thigh + 1.19 * age - 213.65;
  } else {
    vat = 2.15 * waist - 3.63 * thigh + 1.46 * age + 6.22 * bmi - 92.713;
  }

  return { bmi, vat };
}

function interpretVAT(vat) {
  if (vat < 100) {
    return {
      level: "Low Risk",
      color: "green",
      recommendations: [
        "Maintain a Mediterranean-style diet (high in fruits, vegetables, whole grains, olive oil).",
        "Keep regular physical activity: ≥150 min moderate aerobic exercise + 2 strength sessions/week.",
        "Limit processed meats, refined sugars, and trans fats.",
        "Maintain sleep hygiene (7–9 hours/night) and stress reduction practices."
      ]
    };
  } else if (vat >= 100 && vat <= 160) {
    return {
      level: "Moderate Risk",
      color: "orange",
      recommendations: [
        "Adopt calorie deficit (~300–500 kcal/day) with balanced macronutrient distribution.",
        "Prioritize high-fiber foods (vegetables, legumes, whole grains) to improve satiety.",
        "Incorporate resistance training (≥2x/week) in addition to aerobic exercise.",
        "Reduce alcohol to ≤1 drink/day; eliminate sugary beverages.",
        "Track waist circumference every 1–2 months."
      ]
    };
  } else {
    return {
      level: "High Risk",
      color: "red",
      recommendations: [
        "Consult a healthcare professional for individualized dietary and medical advice.",
        "Adopt a Mediterranean/DASH diet, focusing on anti-inflammatory foods (omega-3 fatty acids, nuts, seeds).",
        "Engage in daily physical activity: brisk walking, cycling, resistance training.",
        "Eliminate alcohol, processed foods, and added sugars.",
        "Consider clinical follow-up for cardiometabolic risk factors (lipids, glucose, blood pressure)."
      ]
    };
  }
}

app.post("/api/calculate", (req, res) => {
  const { sex, age, weight, height, waist, thigh } = req.body;

  if (!sex || !age || !weight || !height || !waist || !thigh) {
    return res.status(400).json({ error: "Missing input values" });
  }

  const { bmi, vat } = calculateVAT(sex, age, weight, height, waist, thigh);
  const interpretation = interpretVAT(vat);

  res.json({
    bmi: bmi.toFixed(1),
    vat: vat.toFixed(1),
    interpretation
  });
});

// Premium PDF Report Generation Endpoint
app.post("/api/premium-report", (req, res) => {
  const { sex, age, weight, height, waist, thigh, email, paymentToken } = req.body;

  if (!sex || !age || !weight || !height || !waist || !thigh || !email) {
    return res.status(400).json({ error: "Missing required information" });
  }

  // In production, verify payment token here
  if (!paymentToken) {
    return res.status(400).json({ error: "Payment required for premium report" });
  }

  const { bmi, vat } = calculateVAT(sex, age, weight, height, waist, thigh);
  const interpretation = interpretVAT(vat);

  // Generate AI-powered personalized report
  const premiumReport = generatePremiumReport({
    sex, age, weight, height, waist, thigh, bmi, vat, interpretation
  });

  res.json({
    success: true,
    reportId: generateReportId(),
    downloadUrl: `/api/download-report/${generateReportId()}`,
    report: premiumReport
  });
});

function generateReportId() {
  return 'report_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

function generatePremiumReport(data) {
  const { sex, age, weight, height, waist, thigh, bmi, vat, interpretation } = data;

  return {
    personalInfo: {
      sex, age, weight, height, waist, thigh, bmi, vat
    },
    riskAssessment: {
      level: interpretation.level,
      color: interpretation.color,
      detailedAnalysis: generateDetailedAnalysis(vat, interpretation.level, sex, age)
    },
    nutritionPlan: generateNutritionPlan(vat, interpretation.level, sex, age, weight, height),
    exercisePlan: generateExercisePlan(vat, interpretation.level, sex, age),
    lifestyleRecommendations: generateLifestyleRecommendations(vat, interpretation.level),
    progressTracking: generateProgressTracking(),
    scientificReferences: getScientificReferences()
  };
}

function generateDetailedAnalysis(vat, riskLevel, sex, age) {
  const analyses = {
    "Low Risk": `Your visceral fat level of ${vat} cm² falls within the healthy range. This indicates excellent metabolic health and low cardiovascular risk. At age ${age}, maintaining these levels is crucial for long-term health outcomes.`,
    "Moderate Risk": `Your visceral fat level of ${vat} cm² indicates moderate health risk. This level is associated with increased inflammation and metabolic dysfunction. Immediate lifestyle interventions can significantly improve your health trajectory.`,
    "High Risk": `Your visceral fat level of ${vat} cm² represents a significant health concern. This level dramatically increases your risk of cardiovascular disease, diabetes, and metabolic syndrome. Urgent lifestyle modifications and medical consultation are recommended.`
  };

  return analyses[riskLevel] || "Analysis not available for this risk level.";
}

function generateNutritionPlan(vat, riskLevel, sex, age, weight, height) {
  const bmr = sex === 'male'
    ? 88.362 + (13.397 * weight) + (4.799 * height) - (5.677 * age)
    : 447.593 + (9.247 * weight) + (3.098 * height) - (4.330 * age);

  const targetCalories = riskLevel === "High Risk" ? bmr * 1.4 : bmr * 1.6;

  return {
    dailyCalories: Math.round(targetCalories),
    macroDistribution: {
      protein: "25-30%",
      carbohydrates: "35-40%",
      fats: "30-35%"
    },
    mealPlan: [
      "Breakfast: Greek yogurt with berries and nuts",
      "Lunch: Mediterranean salad with olive oil dressing",
      "Dinner: Grilled fish with vegetables and quinoa",
      "Snacks: Almonds, apple slices, herbal tea"
    ],
    foodsToEmphasize: [
      "Fatty fish (salmon, mackerel, sardines)",
      "Leafy greens and colorful vegetables",
      "Whole grains and legumes",
      "Nuts, seeds, and olive oil",
      "Lean proteins and low-fat dairy"
    ],
    foodsToLimit: [
      "Processed and ultra-processed foods",
      "Sugary beverages and desserts",
      "Refined grains and white bread",
      "Trans fats and excessive saturated fats",
      "Alcohol (limit to 1 drink/day)"
    ]
  };
}

function generateExercisePlan(vat, riskLevel, sex, age) {
  const intensity = riskLevel === "High Risk" ? "moderate" : "moderate to vigorous";

  return {
    weeklyGoals: {
      cardio: "150-300 minutes moderate intensity",
      strength: "2-3 sessions per week",
      flexibility: "Daily stretching or yoga"
    },
    sampleWeek: [
      "Monday: 30min brisk walk + strength training",
      "Tuesday: 20min HIIT workout",
      "Wednesday: 45min moderate cycling + yoga",
      "Thursday: Strength training + 15min walk",
      "Friday: 30min swimming or dancing",
      "Saturday: Hiking or active recreation",
      "Sunday: Gentle yoga and stretching"
    ],
    progressionPlan: [
      "Week 1-2: Build exercise habit with moderate intensity",
      "Week 3-4: Increase duration by 10-15%",
      "Week 5-8: Add high-intensity intervals",
      "Week 9-12: Focus on strength and muscle building"
    ]
  };
}

function generateLifestyleRecommendations(vat, riskLevel) {
  return {
    sleep: {
      target: "7-9 hours per night",
      tips: [
        "Maintain consistent sleep schedule",
        "Create cool, dark sleeping environment",
        "Avoid screens 1 hour before bed",
        "Consider meditation or relaxation techniques"
      ]
    },
    stressManagement: [
      "Practice daily mindfulness or meditation",
      "Engage in regular physical activity",
      "Maintain social connections",
      "Consider professional counseling if needed"
    ],
    hydration: "8-10 glasses of water daily",
    supplementation: [
      "Omega-3 fatty acids (1-2g daily)",
      "Vitamin D (if deficient)",
      "Probiotics for gut health",
      "Consult healthcare provider before starting"
    ]
  };
}

function generateProgressTracking() {
  return {
    measurements: [
      "Weekly waist circumference",
      "Monthly weight and body composition",
      "Quarterly visceral fat reassessment"
    ],
    healthMarkers: [
      "Blood pressure monitoring",
      "Annual lipid panel",
      "HbA1c for diabetes screening",
      "Inflammatory markers (CRP)"
    ],
    milestones: [
      "Month 1: Establish routine and habits",
      "Month 3: 5-10% reduction in waist circumference",
      "Month 6: Improved energy and sleep quality",
      "Month 12: Significant visceral fat reduction"
    ]
  };
}

function getScientificReferences() {
  return [
    "Ryo, M., et al. (2005). Diabetes Care, 28(2), 451-453.",
    "Amato, M. C., et al. (2010). Diabetes Care, 33(4), 920-922.",
    "Yoshizumi, T., et al. (1999). Radiology, 211(1), 283-286.",
    "Despres, J.P. (2012). Circulation, 126(10), 1301-1313.",
    "Sahakyan, K.R., et al. (2015). Annals of Internal Medicine, 163(11), 827-835."
  ];
}

const PORT = process.env.PORT || 3001;
app.listen(PORT, () => console.log(`Backend running on http://localhost:${PORT}`));
