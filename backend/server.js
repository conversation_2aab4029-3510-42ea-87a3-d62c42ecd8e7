const express = require("express");
const bodyParser = require("body-parser");
const cors = require("cors");

const app = express();
app.use(cors());
app.use(bodyParser.json());

/**
 * VISCERAL FAT CALCULATOR - PROPRIETARY SOFTWARE
 * Copyright (c) 2025. All rights reserved.
 *
 * EDUCATIONAL USE ONLY - NOT A MEDICAL DEVICE
 * This software is for educational and research purposes only.
 * It is NOT intended for medical diagnosis, treatment, or clinical decision-making.
 * Always consult qualified healthcare professionals for medical advice.
 *
 * VAT calculation formulas based on validated research:
 *
 * SCIENTIFIC REFERENCES:
 * 1. <PERSON><PERSON>, <PERSON>, et al. (2005). "A new simple method for the measurement of visceral fat accumulation
 *    by bioelectrical impedance analysis." Diabetes Care, 28(2), 451-453.
 * 2. <PERSON>, M. C., et al. (2010). "Visceral Adiposity Index: a reliable indicator of visceral fat
 *    function associated with cardiometabolic risk." Diabetes Care, 33(4), 920-922.
 * 3. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, et al. (1999). "Abdominal fat: standardized technique for measurement at CT."
 *    Radiology, 211(1), 283-286.
 *
 * FORMULAS:
 * - Men: VAT = 6*WC – 4.41*TC + 1.19*Age – 213.65
 * - Women: VAT = 2.15*WC – 3.63*TC + 1.46*Age + 6.22*BMI – 92.713
 *
 * Where: WC = Waist Circumference (cm), TC = Thigh Circumference (cm), BMI = Body Mass Index (kg/m²)
 */
function calculateVAT(sex, age, weight, height, waist, thigh) {
  const bmi = weight / ((height / 100) ** 2);
  let vat;

  if (sex === "male") {
    vat = 6 * waist - 4.41 * thigh + 1.19 * age - 213.65;
  } else {
    vat = 2.15 * waist - 3.63 * thigh + 1.46 * age + 6.22 * bmi - 92.713;
  }

  return { bmi, vat };
}

function interpretVAT(vat) {
  if (vat < 100) {
    return {
      level: "Low Risk",
      color: "green",
      recommendations: [
        "Maintain a Mediterranean-style diet (high in fruits, vegetables, whole grains, olive oil).",
        "Keep regular physical activity: ≥150 min moderate aerobic exercise + 2 strength sessions/week.",
        "Limit processed meats, refined sugars, and trans fats.",
        "Maintain sleep hygiene (7–9 hours/night) and stress reduction practices."
      ]
    };
  } else if (vat >= 100 && vat <= 160) {
    return {
      level: "Moderate Risk",
      color: "orange",
      recommendations: [
        "Adopt calorie deficit (~300–500 kcal/day) with balanced macronutrient distribution.",
        "Prioritize high-fiber foods (vegetables, legumes, whole grains) to improve satiety.",
        "Incorporate resistance training (≥2x/week) in addition to aerobic exercise.",
        "Reduce alcohol to ≤1 drink/day; eliminate sugary beverages.",
        "Track waist circumference every 1–2 months."
      ]
    };
  } else {
    return {
      level: "High Risk",
      color: "red",
      recommendations: [
        "Consult a healthcare professional for individualized dietary and medical advice.",
        "Adopt a Mediterranean/DASH diet, focusing on anti-inflammatory foods (omega-3 fatty acids, nuts, seeds).",
        "Engage in daily physical activity: brisk walking, cycling, resistance training.",
        "Eliminate alcohol, processed foods, and added sugars.",
        "Consider clinical follow-up for cardiometabolic risk factors (lipids, glucose, blood pressure)."
      ]
    };
  }
}

app.post("/api/calculate", (req, res) => {
  const { sex, age, weight, height, waist, thigh } = req.body;

  if (!sex || !age || !weight || !height || !waist || !thigh) {
    return res.status(400).json({ error: "Missing input values" });
  }

  const { bmi, vat } = calculateVAT(sex, age, weight, height, waist, thigh);
  const interpretation = interpretVAT(vat);

  res.json({
    bmi: bmi.toFixed(1),
    vat: vat.toFixed(1),
    interpretation
  });
});

const PORT = process.env.PORT || 3001;
app.listen(PORT, () => console.log(`Backend running on http://localhost:${PORT}`));
