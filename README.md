# Visceral Fat Calculator

A web application for calculating visceral adipose tissue (VAT) using validated formulas for men and women.

## Features

- Calculate VAT using scientifically validated formulas
- Different calculations for men and women
- Risk level interpretation (Low, Moderate, High)
- Personalized health recommendations
- Modern, responsive UI with TailwindCSS

## Quick Start

### Option 1: Use the start script
```bash
./start-app.sh
```

### Option 2: Manual start
1. Start the backend:
```bash
cd backend
node server.js
```

2. Start the frontend (in a new terminal):
```bash
cd frontend
npm run dev
```

## Access the Application

- **Frontend**: http://localhost:5176
- **Backend API**: http://localhost:3001

## API Usage

### Calculate VAT
```bash
curl -X POST http://localhost:3001/api/calculate \
  -H "Content-Type: application/json" \
  -d '{
    "sex": "male",
    "age": 30,
    "weight": 75,
    "height": 175,
    "waist": 85,
    "thigh": 55
  }'
```

## VAT Calculation Formulas

- **Men**: VAT = 6×WC – 4.41×TC + 1.19×Age – 213.65
- **Women**: VAT = 2.15×WC – 3.63×TC + 1.46×Age + 6.22×BMI – 92.713

Where:
- WC = Waist Circumference (cm)
- TC = Thigh Circumference (cm)
- BMI = Body Mass Index (kg/m²)

## Risk Levels

- **Low Risk**: VAT < 100 cm²
- **Moderate Risk**: VAT 100-160 cm²
- **High Risk**: VAT > 160 cm²

## Technology Stack

- **Frontend**: React, Vite, TailwindCSS
- **Backend**: Node.js, Express
- **API**: RESTful JSON API

## Dependencies

### Frontend
- React 18.2.0
- Vite 5.4.19
- TailwindCSS 3.4.0

### Backend
- Express 5.1.0
- CORS 2.8.5
- Body-parser 2.2.0
