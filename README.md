# Visceral Fat Calculator

**PROPRIETARY SOFTWARE - ALL RIGHTS RESERVED**

A web application for calculating visceral adipose tissue (VAT) using validated formulas for men and women.

## ⚠️ IMPORTANT DISCLAIMERS

- **Educational Use Only**: This software is for educational and research purposes only
- **Not a Medical Device**: This tool is NOT intended for medical diagnosis, treatment, or clinical decision-making
- **Consult Healthcare Professionals**: Always seek advice from qualified healthcare professionals for medical concerns
- **Proprietary Software**: This is proprietary software with all rights reserved

## Features

- Calculate VAT using scientifically validated formulas
- Different calculations for men and women
- Risk level interpretation (Low, Moderate, High)
- Personalized health recommendations
- Modern, responsive UI with TailwindCSS

## Quick Start

### Option 1: Use the start script
```bash
./start-app.sh
```

### Option 2: Manual start
1. Start the backend:
```bash
cd backend
node server.js
```

2. Start the frontend (in a new terminal):
```bash
cd frontend
npm run dev
```

## Access the Application

- **Frontend**: http://localhost:5176
- **Backend API**: http://localhost:3001

## API Usage

### Calculate VAT
```bash
curl -X POST http://localhost:3001/api/calculate \
  -H "Content-Type: application/json" \
  -d '{
    "sex": "male",
    "age": 30,
    "weight": 75,
    "height": 175,
    "waist": 85,
    "thigh": 55
  }'
```

## VAT Calculation Formulas

- **Men**: VAT = 6×WC – 4.41×TC + 1.19×Age – 213.65
- **Women**: VAT = 2.15×WC – 3.63×TC + 1.46×Age + 6.22×BMI – 92.713

Where:
- WC = Waist Circumference (cm)
- TC = Thigh Circumference (cm)
- BMI = Body Mass Index (kg/m²)

## Risk Levels

- **Low Risk**: VAT < 100 cm²
- **Moderate Risk**: VAT 100-160 cm²
- **High Risk**: VAT > 160 cm²

## Technology Stack

- **Frontend**: React, Vite, TailwindCSS
- **Backend**: Node.js, Express
- **API**: RESTful JSON API

## Dependencies

### Frontend
- React 18.2.0
- Vite 5.4.19
- TailwindCSS 3.4.0

### Backend
- Express 5.1.0
- CORS 2.8.5
- Body-parser 2.2.0

## Scientific References

The VAT calculation formulas are based on peer-reviewed research:

1. **Ryo, M., et al. (2005).** "A new simple method for the measurement of visceral fat accumulation by bioelectrical impedance analysis." *Diabetes Care*, 28(2), 451-453.

2. **Amato, M. C., et al. (2010).** "Visceral Adiposity Index: a reliable indicator of visceral fat function associated with cardiometabolic risk." *Diabetes Care*, 33(4), 920-922.

3. **Yoshizumi, T., et al. (1999).** "Abdominal fat: standardized technique for measurement at CT." *Radiology*, 211(1), 283-286.

## Commercialization Strategy

### Phase 1: MVP Validation (Current)
- ✅ Core VAT calculation functionality
- ✅ Educational disclaimers and legal protection
- ✅ Scientific references and validation
- ✅ Proprietary licensing structure

### Phase 2: Market Research & Validation
- **Target Markets:**
  - Healthcare education platforms
  - Fitness and wellness apps
  - Medical training institutions
  - Research organizations
  - Corporate wellness programs

- **Market Analysis:**
  - Research existing VAT calculation tools
  - Identify pricing models ($5-50/month SaaS)
  - Survey potential customers (healthcare educators, fitness professionals)
  - Analyze regulatory requirements by region

### Phase 3: Product Enhancement
- **Professional Features:**
  - Batch processing for multiple patients/clients
  - Data export capabilities (PDF reports, CSV)
  - Integration APIs for EMR/fitness platforms
  - Advanced analytics and trend tracking
  - Multi-language support

- **Enterprise Features:**
  - White-label licensing
  - Custom branding options
  - Advanced user management
  - Compliance reporting
  - API rate limiting and authentication

### Phase 4: Go-to-Market Strategy
- **Licensing Models:**
  - Individual licenses: $9.99/month
  - Professional licenses: $29.99/month (multiple users)
  - Enterprise licenses: $99.99/month (unlimited users + API)
  - White-label licensing: Custom pricing

- **Distribution Channels:**
  - Direct B2B sales to healthcare institutions
  - Partnership with medical education companies
  - Integration marketplace (Epic, Cerner, etc.)
  - Fitness platform partnerships (MyFitnessPal, etc.)

### Phase 5: Regulatory & Compliance
- **Important Considerations:**
  - FDA guidance on medical device software
  - HIPAA compliance for healthcare data
  - International medical device regulations
  - Professional liability insurance
  - Terms of service and privacy policies

### Revenue Projections
- **Year 1:** $50K-100K (100-200 professional users)
- **Year 2:** $200K-500K (enterprise clients + API licensing)
- **Year 3:** $500K-1M+ (white-label partnerships)

## Legal & Intellectual Property

- **Copyright Protection:** All code and algorithms are proprietary
- **Trade Secret Protection:** Calculation optimizations and business logic
- **Trademark Considerations:** "Visceral Fat Calculator" branding
- **Patent Potential:** Novel calculation methods or UI innovations

## Next Development Phases

Each phase should be developed independently with separate repositories:

1. **Phase A:** Enhanced UI/UX and mobile responsiveness
2. **Phase B:** User authentication and data persistence
3. **Phase C:** API development and third-party integrations
4. **Phase D:** Enterprise features and white-label solutions
5. **Phase E:** Advanced analytics and reporting

## Contact & Licensing

For commercial licensing inquiries:
- Email: [<EMAIL>]
- Website: [your-company-website.com]
- LinkedIn: [your-professional-profile]

---

**© 2025 [Your Name/Company]. All rights reserved. Proprietary and confidential.**
