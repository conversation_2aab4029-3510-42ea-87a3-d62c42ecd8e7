<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog Admin - Visceral Fat Calculator</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                <div class="flex items-center justify-between">
                    <h1 class="text-2xl font-bold text-gray-900">Blog Administration</h1>
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-500">Visceral Fat Calculator</span>
                        <a href="../index.html" class="text-blue-600 hover:text-blue-800">← Back to Website</a>
                    </div>
                </div>
            </div>
        </header>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
                <div class="grid md:grid-cols-4 gap-4">
                    <button onclick="listPosts()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        📝 List Posts
                    </button>
                    <button onclick="generatePosts()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        🔄 Generate HTML
                    </button>
                    <button onclick="showNewPostForm()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                        ➕ New Post
                    </button>
                    <button onclick="updateListing()" class="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors">
                        🔄 Update Listing
                    </button>
                </div>
            </div>

            <!-- Instructions -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                <h3 class="text-lg font-semibold text-blue-900 mb-3">📚 How to Use the Blog System</h3>
                <div class="grid md:grid-cols-2 gap-6 text-sm text-blue-800">
                    <div>
                        <h4 class="font-semibold mb-2">1. Create Content</h4>
                        <ul class="space-y-1">
                            <li>• Write posts in Markdown format</li>
                            <li>• Save files in <code>blog-content/</code> folder</li>
                            <li>• Use frontmatter for metadata</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-semibold mb-2">2. Generate & Publish</h4>
                        <ul class="space-y-1">
                            <li>• Click "Generate HTML" to convert MD to HTML</li>
                            <li>• Click "Update Listing" to refresh blog page</li>
                            <li>• Files appear in <code>blog/</code> folder</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Command Line Instructions -->
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">💻 Command Line Usage</h3>
                <div class="bg-gray-900 text-green-400 p-4 rounded font-mono text-sm">
                    <div class="mb-2"># Install dependencies</div>
                    <div class="mb-4">cd website && npm run install-deps</div>
                    
                    <div class="mb-2"># List all posts</div>
                    <div class="mb-4">npm run blog:list</div>
                    
                    <div class="mb-2"># Create new post</div>
                    <div class="mb-4">npm run blog:new "Your Post Title"</div>
                    
                    <div class="mb-2"># Generate HTML from markdown</div>
                    <div class="mb-4">npm run blog:generate</div>
                    
                    <div class="mb-2"># Update blog listing page</div>
                    <div>npm run blog:update</div>
                </div>
            </div>

            <!-- File Structure -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">📁 File Structure</h3>
                <div class="bg-gray-50 p-4 rounded font-mono text-sm">
                    <div class="text-blue-600">website/</div>
                    <div class="ml-4">
                        <div class="text-green-600">├── blog-content/</div>
                        <div class="ml-8 text-gray-600">
                            <div>├── what-is-visceral-fat.md</div>
                            <div>└── visceral-fat-chronic-diseases.md</div>
                        </div>
                        <div class="text-green-600">├── blog/</div>
                        <div class="ml-8 text-gray-600">
                            <div>├── what-is-visceral-fat.html</div>
                            <div>└── visceral-fat-chronic-diseases.html</div>
                        </div>
                        <div class="text-green-600">└── admin/</div>
                        <div class="ml-8 text-gray-600">
                            <div>├── blog-manager.js</div>
                            <div>├── blog-cli.js</div>
                            <div>└── index.html (this file)</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Output Area -->
            <div id="output" class="mt-8 hidden">
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Output</h3>
                    <pre id="output-content" class="bg-gray-900 text-green-400 p-4 rounded text-sm overflow-auto max-h-96"></pre>
                </div>
            </div>

            <!-- New Post Form -->
            <div id="new-post-form" class="mt-8 hidden">
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Create New Post</h3>
                    <form onsubmit="createNewPost(event)">
                        <div class="grid md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                                <input type="text" id="post-title" required class="w-full border border-gray-300 rounded-lg px-3 py-2">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                                <select id="post-category" class="w-full border border-gray-300 rounded-lg px-3 py-2">
                                    <option>Health Research</option>
                                    <option>Prevention</option>
                                    <option>Nutrition</option>
                                    <option>Exercise</option>
                                    <option>Risk Assessment</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                            <input type="text" id="post-description" required class="w-full border border-gray-300 rounded-lg px-3 py-2">
                        </div>
                        <div class="flex items-center space-x-4">
                            <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                                Create Post Template
                            </button>
                            <button type="button" onclick="hideNewPostForm()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400">
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showOutput(content) {
            document.getElementById('output').classList.remove('hidden');
            document.getElementById('output-content').textContent = content;
        }

        function hideOutput() {
            document.getElementById('output').classList.add('hidden');
        }

        function showNewPostForm() {
            document.getElementById('new-post-form').classList.remove('hidden');
        }

        function hideNewPostForm() {
            document.getElementById('new-post-form').classList.add('hidden');
        }

        function listPosts() {
            showOutput('This feature requires running the CLI command:\nnpm run blog:list\n\nOr use the command line:\nnode admin/blog-cli.js list');
        }

        function generatePosts() {
            showOutput('This feature requires running the CLI command:\nnpm run blog:generate\n\nOr use the command line:\nnode admin/blog-cli.js generate');
        }

        function updateListing() {
            showOutput('This feature requires running the CLI command:\nnpm run blog:update\n\nOr use the command line:\nnode admin/blog-cli.js update-listing');
        }

        function createNewPost(event) {
            event.preventDefault();
            const title = document.getElementById('post-title').value;
            const category = document.getElementById('post-category').value;
            const description = document.getElementById('post-description').value;
            
            showOutput(`To create this post, run:\nnpm run blog:new "${title}"\n\nOr use the command line:\nnode admin/blog-cli.js new "${title}"\n\nThen edit the generated file in blog-content/ folder.`);
            hideNewPostForm();
        }
    </script>
</body>
</html>
