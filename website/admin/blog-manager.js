const fs = require('fs');
const path = require('path');
const { marked } = require('marked');
const matter = require('gray-matter');

class BlogManager {
    constructor() {
        this.contentDir = path.join(__dirname, '../blog-content');
        this.outputDir = path.join(__dirname, '../blog');
        this.templatesDir = path.join(__dirname, 'templates');

        // Configure marked for better HTML output
        marked.setOptions({
            breaks: true,
            gfm: true,
            headerIds: true,
            headerPrefix: 'heading-'
        });
    }

    // Get all blog posts with metadata
    getAllPosts() {
        const files = fs.readdirSync(this.contentDir)
            .filter(file => file.endsWith('.md'));
        
        const posts = files.map(file => {
            const filePath = path.join(this.contentDir, file);
            const fileContent = fs.readFileSync(filePath, 'utf8');
            const { data, content } = matter(fileContent);
            
            return {
                slug: file.replace('.md', ''),
                filename: file,
                metadata: data,
                content: content,
                wordCount: content.split(' ').length,
                excerpt: this.generateExcerpt(content)
            };
        });

        // Sort by date (newest first)
        return posts.sort((a, b) => new Date(b.metadata.date) - new Date(a.metadata.date));
    }

    // Generate excerpt from content
    generateExcerpt(content, length = 150) {
        const plainText = content.replace(/[#*`\[\]]/g, '').trim();
        return plainText.length > length 
            ? plainText.substring(0, length) + '...'
            : plainText;
    }

    // Convert single markdown post to HTML
    convertPostToHTML(slug) {
        const posts = this.getAllPosts();
        const post = posts.find(p => p.slug === slug);
        
        if (!post) {
            throw new Error(`Post not found: ${slug}`);
        }

        const htmlContent = marked(post.content);
        const template = this.getArticleTemplate();
        
        const html = template
            .replace(/{{title}}/g, post.metadata.title)
            .replace(/{{description}}/g, post.metadata.description)
            .replace(/{{category}}/g, post.metadata.category)
            .replace(/{{author}}/g, post.metadata.author)
            .replace(/{{date}}/g, this.formatDate(post.metadata.date))
            .replace(/{{readTime}}/g, post.metadata.readTime)
            .replace(/{{content}}/g, htmlContent)
            .replace(/{{keywords}}/g, post.metadata.keywords || '');

        return html;
    }

    // Generate all blog posts
    generateAllPosts() {
        const posts = this.getAllPosts();
        const results = [];

        // Ensure output directory exists
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }

        posts.forEach(post => {
            try {
                const html = this.convertPostToHTML(post.slug);
                const outputPath = path.join(this.outputDir, `${post.slug}.html`);
                
                fs.writeFileSync(outputPath, html);
                results.push({
                    slug: post.slug,
                    title: post.metadata.title,
                    status: 'success',
                    path: outputPath
                });
            } catch (error) {
                results.push({
                    slug: post.slug,
                    title: post.metadata.title || 'Unknown',
                    status: 'error',
                    error: error.message
                });
            }
        });

        return results;
    }

    // Update blog listing page
    updateBlogListing() {
        const posts = this.getAllPosts();
        const template = this.getBlogListingTemplate();
        
        const featuredPosts = posts.filter(p => p.metadata.featured);
        const regularPosts = posts.filter(p => !p.metadata.featured);
        
        const featuredHTML = featuredPosts.map(post => this.generatePostCard(post, true)).join('');
        const regularHTML = regularPosts.map(post => this.generatePostCard(post, false)).join('');
        
        const html = template
            .replace('{{featuredPosts}}', featuredHTML)
            .replace('{{regularPosts}}', regularHTML);
        
        const outputPath = path.join(__dirname, '../pages/blog.html');
        fs.writeFileSync(outputPath, html);
        
        return { success: true, path: outputPath };
    }

    // Generate post card HTML
    generatePostCard(post, featured = false) {
        const categoryColors = {
            'Health Research': 'blue',
            'Prevention': 'green',
            'Nutrition': 'purple',
            'Exercise': 'yellow',
            'Risk Assessment': 'red'
        };
        
        const color = categoryColors[post.metadata.category] || 'gray';
        const cardClass = featured ? 'featured-post' : 'regular-post';
        
        return `
            <article class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow ${cardClass}">
                <div class="h-48 bg-gradient-to-br from-${color}-400 to-${color}-600 flex items-center justify-center">
                    <svg class="h-16 w-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="p-6">
                    <div class="flex items-center mb-3">
                        <span class="bg-${color}-100 text-${color}-800 px-2 py-1 rounded text-xs font-medium">${post.metadata.category}</span>
                        <span class="ml-2 text-sm text-gray-500">${post.metadata.readTime}</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">${post.metadata.title}</h3>
                    <p class="text-gray-600 mb-4">${post.excerpt}</p>
                    <a href="../blog/${post.slug}.html" class="text-${color}-600 hover:text-${color}-800 font-medium">Read More →</a>
                </div>
            </article>
        `;
    }

    // Format date for display
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        });
    }

    // Get article template
    getArticleTemplate() {
        const templatePath = path.join(this.templatesDir, 'article-template.html');
        if (fs.existsSync(templatePath)) {
            return fs.readFileSync(templatePath, 'utf8');
        }
        return this.getDefaultArticleTemplate();
    }

    // Get blog listing template
    getBlogListingTemplate() {
        const templatePath = path.join(this.templatesDir, 'blog-listing-template.html');
        if (fs.existsSync(templatePath)) {
            return fs.readFileSync(templatePath, 'utf8');
        }
        return this.getDefaultBlogListingTemplate();
    }

    // Default article template
    getDefaultArticleTemplate() {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}} - Visceral Fat Calculator</title>
    <meta name="description" content="{{description}}">
    <meta name="keywords" content="{{keywords}}">
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="../index.html" class="flex items-center">
                        <h1 class="text-2xl font-bold text-blue-600">Visceral Fat Calculator</h1>
                        <span class="ml-3 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">Educational Tool</span>
                    </a>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="../index.html" class="text-gray-700 hover:text-blue-600">Home</a>
                    <a href="../pages/calculator.html" class="text-gray-700 hover:text-blue-600">Calculator</a>
                    <a href="../pages/blog.html" class="text-gray-700 hover:text-blue-600">Blog</a>
                    <a href="../pages/premium.html" class="text-gray-700 hover:text-blue-600">Premium</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Article -->
    <article class="pt-24">
        <header class="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-16">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex items-center mb-4">
                    <a href="../pages/blog.html" class="text-blue-200 hover:text-white mr-2">← Back to Blog</a>
                    <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">{{category}}</span>
                </div>
                <h1 class="text-4xl md:text-5xl font-bold mb-6">{{title}}</h1>
                <p class="text-xl text-blue-100 mb-6">{{description}}</p>
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                        DR
                    </div>
                    <div class="ml-4">
                        <p class="font-semibold">{{author}}</p>
                        <p class="text-blue-200 text-sm">{{date}} • {{readTime}}</p>
                    </div>
                </div>
            </div>
        </header>

        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="prose prose-lg max-w-none">
                {{content}}
            </div>
        </div>
    </article>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <p class="text-sm text-gray-400">
                    © 2025 Visceral Fat Calculator. All rights reserved. Educational use only.
                </p>
            </div>
        </div>
    </footer>
</body>
</html>`;
    }

    // Default blog listing template (simplified)
    getDefaultBlogListingTemplate() {
        return `<!-- This would be the full blog listing template -->
        <div class="featured-posts">{{featuredPosts}}</div>
        <div class="regular-posts">{{regularPosts}}</div>`;
    }
}

module.exports = BlogManager;
