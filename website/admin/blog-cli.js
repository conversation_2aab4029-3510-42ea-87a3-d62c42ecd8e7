#!/usr/bin/env node

const BlogManager = require('./blog-manager');
const fs = require('fs');
const path = require('path');

const blogManager = new BlogManager();

// Command line interface
const command = process.argv[2];
const args = process.argv.slice(3);

switch (command) {
    case 'list':
        listPosts();
        break;
    case 'generate':
        generatePosts();
        break;
    case 'new':
        createNewPost(args[0]);
        break;
    case 'preview':
        previewPost(args[0]);
        break;
    case 'update-listing':
        updateBlogListing();
        break;
    case 'help':
    default:
        showHelp();
        break;
}

function listPosts() {
    console.log('\n📝 Blog Posts:\n');
    const posts = blogManager.getAllPosts();
    
    posts.forEach((post, index) => {
        const status = post.metadata.featured ? '⭐ FEATURED' : '📄';
        console.log(`${index + 1}. ${status} ${post.metadata.title}`);
        console.log(`   Category: ${post.metadata.category} | ${post.metadata.readTime}`);
        console.log(`   File: ${post.filename} | Words: ${post.wordCount}`);
        console.log('');
    });
}

function generatePosts() {
    console.log('\n🔄 Generating blog posts...\n');
    
    const results = blogManager.generateAllPosts();
    
    results.forEach(result => {
        if (result.status === 'success') {
            console.log(`✅ ${result.title}`);
            console.log(`   Generated: ${result.path}`);
        } else {
            console.log(`❌ ${result.title}`);
            console.log(`   Error: ${result.error}`);
        }
        console.log('');
    });
    
    console.log(`\n📊 Summary: ${results.filter(r => r.status === 'success').length} successful, ${results.filter(r => r.status === 'error').length} errors\n`);
}

function createNewPost(title) {
    if (!title) {
        console.log('❌ Please provide a title for the new post');
        console.log('Usage: node blog-cli.js new "Your Post Title"');
        return;
    }
    
    const slug = title.toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, '-');
    
    const template = `---
title: "${title}"
description: "Brief description of your post"
category: "Health Research"
author: "Dr. Your Name"
date: "${new Date().toISOString().split('T')[0]}"
readTime: "5 min read"
featured: false
keywords: "visceral fat, health"
---

# ${title}

Your content goes here...

## Section 1

Content for section 1.

## Section 2

Content for section 2.

## Conclusion

Wrap up your article here.

[Calculate your visceral fat](/pages/calculator.html) to assess your health risk.

---

**References:**
1. Reference 1
2. Reference 2
`;

    const filename = `${slug}.md`;
    const filepath = path.join(__dirname, '../blog-content', filename);
    
    if (fs.existsSync(filepath)) {
        console.log(`❌ Post already exists: ${filename}`);
        return;
    }
    
    fs.writeFileSync(filepath, template);
    console.log(`✅ Created new post: ${filename}`);
    console.log(`📝 Edit the file: website/blog-content/${filename}`);
    console.log(`🔄 Run 'node blog-cli.js generate' to create HTML`);
}

function previewPost(slug) {
    if (!slug) {
        console.log('❌ Please provide a post slug');
        console.log('Usage: node blog-cli.js preview post-slug');
        return;
    }
    
    try {
        const posts = blogManager.getAllPosts();
        const post = posts.find(p => p.slug === slug);
        
        if (!post) {
            console.log(`❌ Post not found: ${slug}`);
            return;
        }
        
        console.log('\n📖 Post Preview:\n');
        console.log(`Title: ${post.metadata.title}`);
        console.log(`Category: ${post.metadata.category}`);
        console.log(`Author: ${post.metadata.author}`);
        console.log(`Date: ${post.metadata.date}`);
        console.log(`Read Time: ${post.metadata.readTime}`);
        console.log(`Featured: ${post.metadata.featured ? 'Yes' : 'No'}`);
        console.log(`Words: ${post.wordCount}`);
        console.log('\nExcerpt:');
        console.log(post.excerpt);
        console.log('\n');
        
    } catch (error) {
        console.log(`❌ Error previewing post: ${error.message}`);
    }
}

function updateBlogListing() {
    console.log('\n🔄 Updating blog listing page...\n');
    
    try {
        const result = blogManager.updateBlogListing();
        console.log(`✅ Blog listing updated: ${result.path}`);
    } catch (error) {
        console.log(`❌ Error updating blog listing: ${error.message}`);
    }
}

function showHelp() {
    console.log(`
📝 Blog Management CLI

Usage: node blog-cli.js <command> [options]

Commands:
  list              List all blog posts
  generate          Generate HTML from all markdown posts
  new <title>       Create a new blog post template
  preview <slug>    Preview post metadata and excerpt
  update-listing    Update the blog listing page
  help              Show this help message

Examples:
  node blog-cli.js list
  node blog-cli.js new "My New Blog Post"
  node blog-cli.js generate
  node blog-cli.js preview my-new-blog-post

Files:
  📁 blog-content/     Markdown source files
  📁 blog/            Generated HTML files
  📁 admin/           Management tools
`);
}
