<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Premium AI Health Reports - Visceral Fat Calculator</title>
    <meta name="description" content="Get personalized AI-powered PDF health reports with detailed analysis, meal plans, and exercise recommendations based on your visceral fat assessment.">
    
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#2563eb',
                        secondary: '#1e40af',
                        accent: '#dc2626'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="../index.html" class="flex items-center">
                        <h1 class="text-2xl font-bold text-primary">Visceral Fat Calculator</h1>
                        <span class="ml-3 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">Educational Tool</span>
                    </a>
                </div>
                
                <div class="hidden md:flex items-center space-x-8">
                    <a href="../index.html" class="text-gray-700 hover:text-primary">Home</a>
                    <a href="calculator.html" class="text-gray-700 hover:text-primary">Calculator</a>
                    <a href="science.html" class="text-gray-700 hover:text-primary">Science</a>
                    <a href="blog.html" class="text-gray-700 hover:text-primary">Blog</a>
                    <a href="premium.html" class="text-gray-900 hover:text-primary font-medium">Premium</a>
                    <a href="contact.html" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition-colors">Contact</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <section class="bg-gradient-to-r from-yellow-400 to-yellow-600 text-yellow-900 pt-24 pb-12">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="inline-flex items-center bg-white rounded-full px-4 py-2 mb-4">
                <svg class="h-5 w-5 text-yellow-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                </svg>
                <span class="text-sm font-medium">Premium Feature</span>
            </div>
            <h2 class="text-4xl font-bold mb-4">AI-Powered Personal Health Reports</h2>
            <p class="text-xl">Get comprehensive, personalized PDF reports with detailed analysis and actionable recommendations</p>
        </div>
    </section>

    <!-- Feature Comparison -->
    <section class="py-16">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h3 class="text-3xl font-bold text-gray-900 mb-4">Free vs Premium Features</h3>
                <p class="text-xl text-gray-600">Upgrade to get the complete picture of your health</p>
            </div>
            
            <div class="grid md:grid-cols-2 gap-8">
                <!-- Free Features -->
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <div class="text-center mb-6">
                        <h4 class="text-2xl font-bold text-gray-900 mb-2">Free Calculator</h4>
                        <div class="text-3xl font-bold text-green-600">$0</div>
                        <p class="text-gray-600">Always free</p>
                    </div>
                    
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span>Basic visceral fat calculation</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span>BMI calculation</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span>Risk level assessment</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span>Basic recommendations</span>
                        </li>
                        <li class="flex items-center text-gray-400">
                            <svg class="h-5 w-5 text-gray-300 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                            <span>No detailed analysis</span>
                        </li>
                        <li class="flex items-center text-gray-400">
                            <svg class="h-5 w-5 text-gray-300 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                            <span>No PDF report</span>
                        </li>
                    </ul>
                    
                    <a href="calculator.html" class="w-full bg-gray-600 text-white py-3 rounded-lg text-center font-semibold hover:bg-gray-700 transition-colors block">
                        Use Free Calculator
                    </a>
                </div>

                <!-- Premium Features -->
                <div class="bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-lg shadow-xl p-8 text-yellow-900 relative">
                    <div class="absolute top-0 right-0 bg-red-500 text-white px-3 py-1 rounded-bl-lg rounded-tr-lg text-sm font-bold">
                        POPULAR
                    </div>
                    
                    <div class="text-center mb-6">
                        <h4 class="text-2xl font-bold mb-2">Premium Report</h4>
                        <div class="text-3xl font-bold">$9.99</div>
                        <p class="text-yellow-800">Per report</p>
                    </div>
                    
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-yellow-800 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="font-medium">Everything in Free, plus:</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-yellow-800 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span>AI-powered detailed analysis</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-yellow-800 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span>Personalized meal plans</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-yellow-800 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span>Custom exercise routines</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-yellow-800 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span>Professional PDF report</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-yellow-800 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span>Progress tracking templates</span>
                        </li>
                    </ul>
                    
                    <button onclick="openPremiumModal()" class="w-full bg-white text-yellow-600 py-3 rounded-lg text-center font-bold hover:bg-gray-100 transition-colors">
                        Get Premium Report
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- What's Included -->
    <section class="py-16 bg-white">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h3 class="text-3xl font-bold text-gray-900 mb-4">What's Included in Your Premium Report</h3>
                <p class="text-xl text-gray-600">A comprehensive 15-20 page personalized health analysis</p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 00-2 2H9z"></path>
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold mb-2">Detailed Analysis</h4>
                    <p class="text-gray-600">AI-powered interpretation of your results with health implications and risk factors</p>
                </div>
                
                <div class="text-center">
                    <div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16l3-3m-3 3l-3-3"></path>
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold mb-2">Nutrition Plan</h4>
                    <p class="text-gray-600">Personalized meal plans and dietary recommendations to reduce visceral fat</p>
                </div>
                
                <div class="text-center">
                    <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold mb-2">Exercise Program</h4>
                    <p class="text-gray-600">Custom workout routines designed to target visceral fat reduction</p>
                </div>
                
                <div class="text-center">
                    <div class="bg-red-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold mb-2">Lifestyle Recommendations</h4>
                    <p class="text-gray-600">Sleep, stress management, and lifestyle modifications for optimal health</p>
                </div>
                
                <div class="text-center">
                    <div class="bg-yellow-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 00-2 2H9z"></path>
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold mb-2">Progress Tracking</h4>
                    <p class="text-gray-600">Templates and tools to monitor your improvement over time</p>
                </div>
                
                <div class="text-center">
                    <div class="bg-indigo-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold mb-2">Scientific References</h4>
                    <p class="text-gray-600">Peer-reviewed research supporting all recommendations and analysis</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Sample Report Preview -->
    <section class="py-16 bg-gray-100">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h3 class="text-3xl font-bold text-gray-900 mb-4">Sample Report Preview</h3>
                <p class="text-xl text-gray-600">See what you'll receive in your personalized PDF report</p>
            </div>
            
            <div class="bg-white rounded-lg shadow-xl p-8">
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                    <svg class="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h4 class="text-xl font-semibold text-gray-900 mb-2">Professional PDF Report</h4>
                    <p class="text-gray-600 mb-4">15-20 pages of personalized health analysis and recommendations</p>
                    <button onclick="openSampleModal()" class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-secondary transition-colors">
                        View Sample Pages
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 bg-gradient-to-r from-primary to-secondary">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h3 class="text-3xl font-bold text-white mb-4">Ready to Get Your Personalized Health Report?</h3>
            <p class="text-xl text-blue-100 mb-8">
                Start with our free calculator, then upgrade to get your comprehensive AI-powered analysis
            </p>
            
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="calculator.html" class="bg-white text-primary px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors">
                    Start Free Calculator
                </a>
                <button onclick="openPremiumModal()" class="bg-yellow-400 text-blue-900 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-yellow-300 transition-colors">
                    Get Premium Report - $9.99
                </button>
            </div>
        </div>
    </section>

    <!-- Premium Modal -->
    <div id="premium-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-md w-full p-6">
                <div class="text-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">Get Your Premium Report</h3>
                    <p class="text-gray-600">Complete your assessment first, then upgrade to premium</p>
                </div>
                
                <div class="space-y-4">
                    <a href="calculator.html" class="w-full bg-primary text-white py-3 rounded-lg text-center font-semibold hover:bg-secondary transition-colors block">
                        1. Complete Free Assessment
                    </a>
                    <div class="w-full bg-gray-200 text-gray-500 py-3 rounded-lg text-center font-semibold">
                        2. Upgrade to Premium ($9.99)
                    </div>
                    <div class="w-full bg-gray-200 text-gray-500 py-3 rounded-lg text-center font-semibold">
                        3. Download Your PDF Report
                    </div>
                </div>
                
                <button onclick="closePremiumModal()" class="mt-6 w-full border border-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                    Close
                </button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <p class="text-sm text-gray-400">
                    © 2025 Visceral Fat Calculator. All rights reserved. Educational use only.
                    <br>
                    <strong>Not a medical device.</strong> Always consult healthcare professionals for medical advice.
                </p>
            </div>
        </div>
    </footer>

    <script>
        function openPremiumModal() {
            document.getElementById('premium-modal').classList.remove('hidden');
        }
        
        function closePremiumModal() {
            document.getElementById('premium-modal').classList.add('hidden');
        }
        
        function openSampleModal() {
            alert('Sample report preview would open here. This would show sample pages from the AI-generated PDF report.');
        }
    </script>
</body>
</html>
