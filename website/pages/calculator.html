<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visceral Fat Calculator - Free Assessment Tool</title>
    <meta name="description" content="Calculate your visceral fat levels instantly using scientifically validated formulas. Get your risk assessment and personalized recommendations.">
    
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#2563eb',
                        secondary: '#1e40af',
                        accent: '#dc2626'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="../index.html" class="flex items-center">
                        <h1 class="text-2xl font-bold text-primary">Visceral Fat Calculator</h1>
                        <span class="ml-3 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">Educational Tool</span>
                    </a>
                </div>
                
                <div class="hidden md:flex items-center space-x-8">
                    <a href="../index.html" class="text-gray-700 hover:text-primary">Home</a>
                    <a href="calculator.html" class="text-gray-900 hover:text-primary font-medium">Calculator</a>
                    <a href="science.html" class="text-gray-700 hover:text-primary">Science</a>
                    <a href="blog.html" class="text-gray-700 hover:text-primary">Blog</a>
                    <a href="premium.html" class="text-gray-700 hover:text-primary">Premium</a>
                    <a href="contact.html" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition-colors">Contact</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <section class="bg-gradient-to-r from-primary to-secondary text-white pt-24 pb-12">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-4xl font-bold mb-4">Visceral Fat Calculator</h2>
            <p class="text-xl text-blue-100">Get your visceral fat assessment in seconds using scientifically validated formulas</p>
        </div>
    </section>

    <!-- Educational Disclaimer -->
    <section class="py-8 bg-yellow-50 border-b-2 border-yellow-200">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-yellow-800">
                        <strong>Educational Use Only:</strong> This tool is for educational purposes only and is NOT a medical device. 
                        Always consult healthcare professionals for medical advice and diagnosis.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Calculator Section -->
    <section class="py-12">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid lg:grid-cols-2 gap-12">
                <!-- Calculator Form -->
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <h3 class="text-2xl font-bold text-gray-900 mb-6">Calculate Your Visceral Fat</h3>
                    
                    <form id="vat-calculator-form" class="space-y-6">
                        <div>
                            <label class="block text-gray-700 font-medium mb-2">Sex</label>
                            <select id="sex" required class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent">
                                <option value="male">Male</option>
                                <option value="female">Female</option>
                            </select>
                        </div>

                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-gray-700 font-medium mb-2">Age (years)</label>
                                <input type="number" id="age" required min="18" max="100" 
                                       class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent"
                                       placeholder="e.g., 35">
                            </div>
                            <div>
                                <label class="block text-gray-700 font-medium mb-2">Weight (kg)</label>
                                <input type="number" id="weight" required min="30" max="300" step="0.1"
                                       class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent"
                                       placeholder="e.g., 70.5">
                            </div>
                        </div>

                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-gray-700 font-medium mb-2">Height (cm)</label>
                                <input type="number" id="height" required min="120" max="250"
                                       class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent"
                                       placeholder="e.g., 175">
                            </div>
                            <div>
                                <label class="block text-gray-700 font-medium mb-2">Waist Circumference (cm)</label>
                                <input type="number" id="waist" required min="50" max="200" step="0.1"
                                       class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent"
                                       placeholder="e.g., 85.5">
                            </div>
                        </div>

                        <div>
                            <label class="block text-gray-700 font-medium mb-2">Thigh Circumference (cm)</label>
                            <input type="number" id="thigh" required min="30" max="100" step="0.1"
                                   class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="e.g., 55.0">
                            <p class="text-sm text-gray-500 mt-1">Measure at the widest part of your thigh</p>
                        </div>

                        <button type="submit" 
                                class="w-full bg-primary text-white py-4 rounded-lg text-lg font-semibold hover:bg-secondary transition-colors">
                            Calculate My Visceral Fat
                        </button>
                    </form>
                </div>

                <!-- Results Section -->
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <h3 class="text-2xl font-bold text-gray-900 mb-6">Your Results</h3>
                    
                    <div id="results-placeholder" class="text-center py-12">
                        <svg class="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 00-2 2H9z"></path>
                        </svg>
                        <p class="text-gray-500">Enter your measurements to see your visceral fat assessment</p>
                    </div>

                    <div id="results-content" class="hidden">
                        <div class="space-y-6">
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Body Mass Index (BMI)</h4>
                                <div class="text-2xl font-bold text-blue-600" id="bmi-result">--</div>
                                <p class="text-sm text-gray-600">kg/m²</p>
                            </div>

                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Visceral Adipose Tissue (VAT)</h4>
                                <div class="text-3xl font-bold text-primary" id="vat-result">--</div>
                                <p class="text-sm text-gray-600">cm²</p>
                            </div>

                            <div class="border-l-4 p-4 rounded-r-lg" id="risk-assessment">
                                <h4 class="font-semibold mb-2" id="risk-level">Risk Level</h4>
                                <p class="text-sm" id="risk-description">Risk description will appear here</p>
                            </div>

                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Personalized Recommendations</h4>
                                <ul class="space-y-2 text-sm" id="recommendations-list">
                                    <!-- Recommendations will be populated here -->
                                </ul>
                            </div>

                            <!-- Premium Upgrade CTA -->
                            <div class="bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-lg p-6 text-center">
                                <h4 class="text-lg font-bold text-yellow-900 mb-2">Want More Detailed Analysis?</h4>
                                <p class="text-yellow-800 mb-4">Get an AI-powered personalized PDF report with detailed recommendations, meal plans, and exercise routines.</p>
                                <a href="premium.html" class="bg-white text-yellow-600 px-6 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                                    Upgrade to Premium
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- How to Measure Section -->
    <section class="py-16 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-8 text-center">How to Take Accurate Measurements</h3>
            
            <div class="grid md:grid-cols-2 gap-8">
                <div class="bg-blue-50 rounded-lg p-6">
                    <h4 class="text-lg font-semibold text-blue-900 mb-3">Waist Circumference</h4>
                    <ul class="text-sm text-blue-800 space-y-2">
                        <li>• Stand upright with feet together</li>
                        <li>• Locate the top of your hip bone</li>
                        <li>• Measure around your waist at this level</li>
                        <li>• Keep the tape measure level and snug</li>
                        <li>• Breathe normally and measure after exhaling</li>
                    </ul>
                </div>
                
                <div class="bg-green-50 rounded-lg p-6">
                    <h4 class="text-lg font-semibold text-green-900 mb-3">Thigh Circumference</h4>
                    <ul class="text-sm text-green-800 space-y-2">
                        <li>• Stand with feet slightly apart</li>
                        <li>• Find the widest part of your thigh</li>
                        <li>• Usually about 1/3 down from hip to knee</li>
                        <li>• Wrap tape measure around thigh</li>
                        <li>• Keep tape level and comfortably snug</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <p class="text-sm text-gray-400">
                    © 2025 Visceral Fat Calculator. All rights reserved. Educational use only.
                    <br>
                    <strong>Not a medical device.</strong> Always consult healthcare professionals for medical advice.
                </p>
            </div>
        </div>
    </footer>

    <script>
        document.getElementById('vat-calculator-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = {
                sex: document.getElementById('sex').value,
                age: parseInt(document.getElementById('age').value),
                weight: parseFloat(document.getElementById('weight').value),
                height: parseInt(document.getElementById('height').value),
                waist: parseFloat(document.getElementById('waist').value),
                thigh: parseFloat(document.getElementById('thigh').value)
            };
            
            try {
                const response = await fetch('http://localhost:3001/api/calculate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                });
                
                if (!response.ok) {
                    throw new Error('Calculation failed');
                }
                
                const result = await response.json();
                displayResults(result);
                
            } catch (error) {
                console.error('Error:', error);
                alert('Error calculating results. Please check your inputs and try again.');
            }
        });
        
        function displayResults(result) {
            // Hide placeholder and show results
            document.getElementById('results-placeholder').classList.add('hidden');
            document.getElementById('results-content').classList.remove('hidden');
            
            // Update BMI and VAT
            document.getElementById('bmi-result').textContent = result.bmi;
            document.getElementById('vat-result').textContent = result.vat;
            
            // Update risk assessment
            const riskAssessment = document.getElementById('risk-assessment');
            const riskLevel = document.getElementById('risk-level');
            const riskDescription = document.getElementById('risk-description');
            
            riskLevel.textContent = result.interpretation.level;
            
            // Set colors based on risk level
            riskAssessment.className = 'border-l-4 p-4 rounded-r-lg ';
            if (result.interpretation.color === 'green') {
                riskAssessment.className += 'border-green-500 bg-green-50';
                riskLevel.className = 'font-semibold mb-2 text-green-800';
                riskDescription.textContent = 'Your visceral fat levels are within the healthy range. Continue maintaining your current lifestyle.';
            } else if (result.interpretation.color === 'orange') {
                riskAssessment.className += 'border-orange-500 bg-orange-50';
                riskLevel.className = 'font-semibold mb-2 text-orange-800';
                riskDescription.textContent = 'Your visceral fat levels are moderately elevated. Consider lifestyle modifications to reduce health risks.';
            } else {
                riskAssessment.className += 'border-red-500 bg-red-50';
                riskLevel.className = 'font-semibold mb-2 text-red-800';
                riskDescription.textContent = 'Your visceral fat levels are high. Consult with a healthcare professional for personalized advice.';
            }
            
            // Update recommendations
            const recommendationsList = document.getElementById('recommendations-list');
            recommendationsList.innerHTML = '';
            result.interpretation.recommendations.forEach(rec => {
                const li = document.createElement('li');
                li.className = 'flex items-start';
                li.innerHTML = `
                    <svg class="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-gray-700">${rec}</span>
                `;
                recommendationsList.appendChild(li);
            });
        }
    </script>
</body>
</html>
