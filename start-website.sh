#!/bin/bash

# Start the Complete Visceral Fat Calculator Website
echo "🚀 Starting Visceral Fat Calculator Platform..."

# Start backend in background
echo "📡 Starting backend API server..."
cd backend && node server.js &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 2

# Start frontend in background
echo "⚛️  Starting React calculator..."
cd ../frontend && npm run dev &
FRONTEND_PID=$!

# Wait for frontend to start
sleep 3

# Open the main website
echo "🌐 Opening website..."
if command -v open &> /dev/null; then
    # macOS
    open "file://$(pwd)/../website/index.html"
elif command -v xdg-open &> /dev/null; then
    # Linux
    xdg-open "file://$(pwd)/../website/index.html"
elif command -v start &> /dev/null; then
    # Windows
    start "file://$(pwd)/../website/index.html"
fi

echo ""
echo "✅ Visceral Fat Calculator Platform is now running!"
echo ""
echo "🌐 Website: file://$(pwd)/../website/index.html"
echo "📊 Calculator: http://localhost:5176"
echo "📡 API: http://localhost:3001"
echo ""
echo "📝 Navigation:"
echo "   • Homepage: Main landing page with overview"
echo "   • Calculator: Free visceral fat assessment"
echo "   • Premium: AI-powered PDF reports ($9.99)"
echo "   • Blog: Health articles and research"
echo ""
echo "Press Ctrl+C to stop all servers"

# Function to cleanup processes on exit
cleanup() {
    echo ""
    echo "🛑 Stopping servers..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    echo "✅ All servers stopped"
    exit
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Wait for processes
wait
